commit 610d7af98e3ac74ed6ea288df4bd933099f10a68
Author: ael-bagh <<EMAIL>>
Date:   Mon May 19 15:41:48 2025 +0100

    Updates email footer links to use frontend base URL
    
    Replaces hardcoded URLs in the email footer template with dynamic
    URLs based on the  variable. This ensures URL
    consistency and improves maintainability across environments.

diff --git a/app/code/Comave/Marketplace/Setup/Patch/Data/UpdateFooterEmailTemplate.php b/app/code/Comave/Marketplace/Setup/Patch/Data/UpdateFooterEmailTemplate.php
new file mode 100644
index 000000000..da1788b0b
--- /dev/null
+++ b/app/code/Comave/Marketplace/Setup/Patch/Data/UpdateFooterEmailTemplate.php
@@ -0,0 +1,82 @@
+<?php
+
+declare(strict_types=1);
+
+namespace Comave\Marketplace\Setup\Patch\Data;
+
+use Magento\Framework\Setup\Patch\DataPatchInterface;
+use Magento\Framework\Setup\ModuleDataSetupInterface;
+use Magento\Framework\Exception\CouldNotSaveException;
+use Magento\Framework\Module\Dir;
+use Magento\Framework\Module\Dir\Reader as DirReader;
+use Magento\Framework\Filesystem\Io\File as IoFile;
+use Magento\Framework\App\DeploymentConfig;
+use Psr\Log\LoggerInterface;
+
+class UpdateFooterEmailTemplate implements DataPatchInterface
+{
+    private const EMAIL_TEMPLATE_FILE = 'email_footer_template.html';
+    private const ORIG_TEMPLATE_CODE = 'design_email_footer_template'; // <-- confirmed
+    private const DEFAULT_SOURCE_DIR = 'install-data';
+
+    public function __construct(
+        private readonly ModuleDataSetupInterface $moduleDataSetup,
+        private readonly DirReader $dirReader,
+        private readonly IoFile $ioFile,
+        private readonly DeploymentConfig $deploymentConfig,
+        private readonly LoggerInterface $logger
+    ) {
+    }
+
+    public function apply(): void
+    {
+        $this->moduleDataSetup->getConnection()->startSetup();
+
+        $htmlContent = $this->getFileContent(self::EMAIL_TEMPLATE_FILE);
+        if (!$htmlContent) {
+            return;
+        }
+
+        // Replace the frontend_base_url placeholder
+        $frontendBaseUrl = $this->deploymentConfig->get('frontend_base_url') ?? '';
+        $htmlContent = str_replace('{{frontend_base_url}}', rtrim($frontendBaseUrl, '/'), $htmlContent);
+
+        try {
+            $this->moduleDataSetup->getConnection()->update(
+                $this->moduleDataSetup->getTable('email_template'),
+                ['template_text' => $htmlContent],
+                ['orig_template_code = ?' => self::ORIG_TEMPLATE_CODE]
+            );
+        } catch (CouldNotSaveException $e) {
+            $this->logger->error('Failed to update the email template: ' . $e->getMessage());
+        }
+
+        $this->moduleDataSetup->getConnection()->endSetup();
+    }
+
+    private function getSourceDir(): string
+    {
+        return $this->dirReader->getModuleDir(Dir::MODULE_ETC_DIR, 'Comave_Marketplace')
+            . '/' . self::DEFAULT_SOURCE_DIR . '/';
+    }
+
+    private function getFileContent(string $file): string
+    {
+        try {
+            return $this->ioFile->read($this->getSourceDir() . $file);
+        } catch (\Exception $e) {
+            $this->logger->error(sprintf('Template file "%s" does not exist. Error: %s', $file, $e->getMessage()));
+            return '';
+        }
+    }
+
+    public static function getDependencies(): array
+    {
+        return [];
+    }
+
+    public function getAliases(): array
+    {
+        return [];
+    }
+}
diff --git a/app/code/Comave/Marketplace/etc/install-data/email_footer_template.html b/app/code/Comave/Marketplace/etc/install-data/email_footer_template.html
index f43866a0f..47d32657e 100644
--- a/app/code/Comave/Marketplace/etc/install-data/email_footer_template.html
+++ b/app/code/Comave/Marketplace/etc/install-data/email_footer_template.html
@@ -13,8 +13,8 @@
         <tr style="">
             <td style="vertical-align: middle; text-align: center; width: 45%; ">
                 {{block class="Magento\Cms\Block\Block" area="frontend" block_id="weltpixel_social_media_email_block" }}
-                <a  style="font-weight: bold; padding: 0 10px;text-decoration: none;color: #5f6368;font-size:13px;" href="{{var this.getUrl($store,'terms-condition-comave',[],_nosid:1])}}" target="_blank" >{{trans 'TERMS & CONDITIONS'}}</a>
-                <a  style=" font-weight: bold; padding: 0 10px;text-decoration: none;color:#5f6368;font-size:13px;" href="{{var this.getUrl($store,'contact',[],_nosid:1])}}" target="_blank" >{{trans 'CONTACT'}}</a>
+                <a  style="font-weight: bold; padding: 0 10px;text-decoration: none;color: #5f6368;font-size:13px;" href="{{frontend_base_url}}/terms-and-conditions" target="_blank" >{{trans 'TERMS & CONDITIONS'}}</a>
+                <a  style=" font-weight: bold; padding: 0 10px;text-decoration: none;color:#5f6368;font-size:13px;" href="{{frontend_base_url}}/contact-us" target="_blank" >{{trans 'CONTACT'}}</a>
 
             </td>
             <td style="vertical-align: middle; text-align: center;width: 45%;">
