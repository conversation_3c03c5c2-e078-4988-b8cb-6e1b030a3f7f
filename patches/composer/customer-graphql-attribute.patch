--- a/Model/Customer/GetCustomSelectedOptionAttributes.php	2025-04-29 22:46:14
+++ b/Model/Customer/GetCustomSelectedOptionAttributes.php	2025-05-21 10:41:11
@@ -53,13 +53,15 @@
         );

         $result = [];
-        $selectedValues = explode(',', $customAttribute['value']);
+        $selectedValues = is_array($customAttribute['value']) ?
+            $customAttribute['value'] :
+            explode(',', (string) $customAttribute['value']);
         foreach ($attr->getOptions() as $option) {
             if (!in_array($option->getValue(), $selectedValues)) {
                 continue;
             }
             $result[] = [
-                'uid' => $this->uid->encode($option->getValue()),
+                'uid' => $this->uid->encode((string) $option->getValue()),
                 'value' => $option->getValue(),
                 'label' => $option->getLabel()
             ];
