--- a/vendor/magento/module-login-as-customer-frontend-ui/view/frontend/web/js/view/loginAsCustomer.js
+++ b/vendor/magento/module-login-as-customer-frontend-ui/view/frontend/web/js/view/loginAsCustomer.js
@@ -63,7 +63,7 @@
             }
 
             if (this.fullname !== undefined && this.websiteName !== undefined) {
-                this.notificationText($.mage.__('You are connected as <strong>%1</strong> on %2')
+                this.notificationText($.mage.__('(ADMIN MODE) You are connected as <strong>%1</strong> on %2')
                     .replace('%1', _.escape(this.fullname))
                     .replace('%2', _.escape(this.websiteName)));
             }
