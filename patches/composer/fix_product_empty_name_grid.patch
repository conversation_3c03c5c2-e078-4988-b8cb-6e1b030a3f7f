diff --git a/vendor/magento/module-catalog/Ui/Component/Listing/Columns/Thumbnail.php b/vendor/magento/module-catalog/Ui/Component/Listing/Columns/Thumbnail.php
--- a/vendor/magento/module-catalog/Ui/Component/Listing/Columns/Thumbnail.php
+++ b/vendor/magento/module-catalog/Ui/Component/Listing/Columns/Thumbnail.php	(date 1747805053903)
@@ -88,6 +88,6 @@
     {
         $altField = $this->getData('config/altField') ?: self::ALT_FIELD;
         // phpcs:disable Magento2.Functions.DiscouragedFunction
-        return html_entity_decode($row[$altField], ENT_QUOTES, "UTF-8") ?? null;
+        return html_entity_decode($row[$altField]??'notset', ENT_QUOTES, "UTF-8") ?? null;
     }
 }
diff --git a/vendor/magento/module-catalog/Ui/Component/Listing/Columns/ProductActions.php b/vendor/magento/module-catalog/Ui/Component/Listing/Columns/ProductActions.php
--- a/vendor/magento/module-catalog/Ui/Component/Listing/Columns/ProductActions.php
+++ b/vendor/magento/module-catalog/Ui/Component/Listing/Columns/ProductActions.php	(date 1747805031256)
@@ -58,7 +58,7 @@
                         'catalog/product/edit',
                         ['id' => $item['entity_id'], 'store' => $storeId]
                     ),
-                    'ariaLabel' => __('Edit ') . $item['name'],
+                    'ariaLabel' => __('Edit ') . $item["name"]?? 'notset',
                     'label' => __('Edit'),
                     'hidden' => false,
                 ];
