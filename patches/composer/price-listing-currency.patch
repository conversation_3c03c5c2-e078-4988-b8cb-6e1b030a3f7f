--- a/vendor/magento/module-catalog/Ui/Component/Listing/Columns/Price.php	2025-05-21 17:28:14
+++ b/vendor/magento/module-catalog/Ui/Component/Listing/Columns/Price-fix.php	2025-05-21 17:23:19
@@ -74,10 +74,15 @@
             );
 
             $fieldName = $this->getData('name');
-            foreach ($dataSource['data']['items'] as & $item) {
+            $shouldConvert = $store->getBaseCurrencyCode() !== $store->getCurrentCurrencyCode();
+            foreach ($dataSource['data']['items'] as &$item) {
                 if (isset($item[$fieldName])) {
+                    $price = (float) $item[$fieldName];
+                    if ($shouldConvert) {
+                        $price = $this->priceCurrency->convert($price, $store);
+                    }
                     $item[$fieldName] = $this->priceCurrency->format(
-                        sprintf("%F", $item[$fieldName]),
+                        $price,
                         false,
                         PriceCurrencyInterface::DEFAULT_PRECISION,
                         $store
