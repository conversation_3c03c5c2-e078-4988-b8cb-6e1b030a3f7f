# Comave Customisations

In the System Configuration section, you can find the ComAve RMA Settings under Sales > Sales > RMA Settings > Comave Settings

![Comave System Config](files/img.png)

For reference, included postman collection of the entire flow and how it should work:

[Postman collection](files/magento2-rma-graphql.postman_collection.json)

# Magento 2 RMA GraphQL Integration Guide

This guide outlines how to manage the Return Merchandise Authorization (RMA) process using Magento 2 GraphQL APIs. It covers all mutation and query operations necessary to create, manage, and track return requests.

## Prerequisites

- Magento 2.4.x with GraphQL and RMA modules enabled
- Valid customer authentication token
- Postman or any GraphQL client

---

## 1. Check if RMA is Enabled

**Query:**

```graphql
query {
  storeConfig {
    returns_enabled
  }
}
```

---

## 2. List Customer Orders with Eligible Return Items

**Query:**

```graphql
query {
  customer {
    orders {
      items {
        id
        items {
          id
          product_name
          eligible_for_return
        }
      }
    }
  }
}
```

---

## 3. Initiate a Return Request
**Get Reason List**
```graphql
query {
  customAttributeMetadataV2(
    attributes: [
      { attribute_code: "reason", entity_type: "rma_item" }
    ]
  ) {
    items {
      code
      label
      entity_type
      frontend_input
      is_required
      options {
        label
        value #used to set the selected options below as base64_encoded
      }
    }
    errors {
      type
      message
    }
  }
}
```
**Mutation:**

```graphql
mutation {
  requestReturn(
    input: {
      order_uid: "ORDER_UID"
      contact_email: "<EMAIL>"
      comment_text: "Reason for return"
      items: [
        {
          order_item_uid: "ITEM_UID"
          quantity_to_return: 1
          "selected_custom_attributes": [
                {
                    "attribute_code": "reason",
                    "value": "ODE=" #base64 encoded string from the customAttributesV2 query above in the options
                }
            ]
        }
      ]
    }
  ) {
    return {
      uid
      seller {
        name
        address
        phone
        email_info
      }
      number
      status
      items {
        uid
        custom_attributesV2 {
              code
              ... on AttributeSelectedOptions {
                selected_options {
                  label
                  value
                }
              }
        }
      }
    }
  }
}
```

---

## 4. View Customer Returns

**Query:**

```graphql
query {
  customer {
    returns {
      items {
      seller {
        name
        address
        phone
        email_info
      }
        uid
        number
        status
      }
    }
  }
}
```

---

## 5. View a Specific Return

**Query:**

```graphql
query {
  customer {
    return(uid: "RETURN_UID") {
      number
      seller {
        name
        address
        phone
        email_info
      }
      status
      items {
        status
        request_quantity
      }
    }
  }
}
```

---

## 6. Add a Comment to a Return

**Mutation:**

```graphql
mutation {
  addReturnComment(
    input: {
      return_uid: "RETURN_UID"
      comment_text: "Additional details"
    }
  ) {
    return {
      uid
      seller {
        name
        address
        phone
        email_info
      }
      comments {
        author_name
        text
      }
    }
  }
}
```

---

## 7. Add Return Tracking Information

**Mutation:**

```graphql
mutation {
  addReturnTracking(
    input: {
      return_uid: "RETURN_UID"
      carrier_uid: "CARRIER_UID"
      tracking_number: "TRACK123456"
    }
  ) {
    return_shipping_tracking {
      tracking_number
      status {
        text
        type
      }
    }
  }
}
```

---

## 8. Remove Tracking from a Return

**Mutation:**

```graphql
mutation {
  removeReturnTracking(
    input: {
      return_shipping_tracking_uid: "TRACKING_UID"
    }
  ) {
    return {
      uid
      shipping {
        tracking {
          uid
        }
      }
    }
  }
}
```
