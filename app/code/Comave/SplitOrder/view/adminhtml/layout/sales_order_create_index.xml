<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="order_item_extra_info">
            <block name="seller.info" template="Comave_SplitOrder::sales/order/create/seller-info.phtml">
                <arguments>
                    <argument xsi:type="object" name="userInfoViewModel">Comave\SplitOrder\ViewModel\UserInfo</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>
