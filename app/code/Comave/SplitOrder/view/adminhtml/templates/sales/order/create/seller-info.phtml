<?php
/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Quote\Api\Data\CartItemInterface $item */
/** @var \Magento\Framework\Escaper $escaper */
$item = $block->getParentBlock()->getData('item');
$userInfoVm = $block->getData('userInfoViewModel');
$userInfo = [];

if ($userInfoVm instanceof \Comave\SplitOrder\ViewModel\UserInfo) {
    $userInfo = $userInfoVm->getUserInfo((int) $item->getProduct()->getId());
}
?>
<?php if (!empty($userInfo)): ?>
    <tr class="row-seller-info">
        <td colspan="7">
            <strong>
                <?= $escaper->escapeHtml(__('By Seller')) ?>:
            </strong>
            <a target="_blank" href=<?= $escaper->escapeUrl($userInfo['url']);?>>
                <?= $escaper->escapeHtml($userInfo['name'])?>
            </a>
        </td>
    </tr>
<?php endif; ?>
