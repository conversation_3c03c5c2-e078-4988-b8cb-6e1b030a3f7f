<?php
declare(strict_types=1);

namespace Comave\EmailConfig\Model\Config;

use Magento\Framework\App\Config\ScopeConfigInterface;

class Config
{
    private const string XML_PATH_FRONTEND_UNSECURED_URL = 'web/unsecure/frontend_base_url';
    private const string XML_PATH_FRONTEND_SECURED_URL = 'web/secure/frontend_base_url';

    private const ENVIRONMENT_DOMAINS = [
        'production' => [
            'backend' => 'mc.comave.com',
            'frontend' => 'comave.com'
        ],
        'staging' => [
            'backend' => 'mcstaging.comave.com',
            'frontend' => 'staging.comave.com'
        ],
        'development' => [
            'backend' => 'mcdev.comave.com',
            'frontend' => 'dev.comave.com'
        ],
        'local' => [
            'backend' => 'localhost:8080',
            'frontend' => 'localhost:3000'
        ]
    ];

    public function __construct(
        private readonly ScopeConfigInterface $storeConfig
    ) {
    }

    public function getFrontendSecureUrl($scopeType = 'default', $scopeCode = null): string
    {
        return (string) $this->storeConfig->getValue(
            self::XML_PATH_FRONTEND_SECURED_URL,
            $scopeType,
            $scopeCode
        ) ?: '';
    }

    public function getFrontendUnsecureUrl($scopeType = 'default', $scopeCode = null): string
    {
        return (string) $this->storeConfig->getValue(
            self::XML_PATH_FRONTEND_UNSECURED_URL,
            $scopeType,
            $scopeCode
        ) ?: '';
    }

    /**
     * Detect current environment based on backend domain
     *
     * @return string|null
     */
    public function getCurrentEnvironment(): ?string
    {
        $baseUrl = $this->storeConfig->getValue('web/unsecure/base_url')
                   ?: $this->storeConfig->getValue('web/secure/base_url');

        if (!$baseUrl) {
            return null;
        }

        $parsedUrl = parse_url($baseUrl);
        $currentDomain = $parsedUrl['host'] ?? '';

        if (isset($parsedUrl['port']) && !in_array($parsedUrl['port'], [80, 443])) {
            $currentDomain .= ':' . $parsedUrl['port'];
        }

        foreach (self::ENVIRONMENT_DOMAINS as $env => $domains) {
            if ($domains['backend'] === $currentDomain) {
                return $env;
            }
        }

        return null;
    }

    /**
     * Get frontend domain for current environment
     *
     * @return string|null
     */
    public function getCurrentFrontendDomain(): ?string
    {
        $environment = $this->getCurrentEnvironment();

        if (!$environment || !isset(self::ENVIRONMENT_DOMAINS[$environment])) {
            return null;
        }

        return self::ENVIRONMENT_DOMAINS[$environment]['frontend'];
    }

    /**
     * Get backend domain for current environment
     *
     * @return string|null
     */
    public function getCurrentBackendDomain(): ?string
    {
        $environment = $this->getCurrentEnvironment();

        if (!$environment || !isset(self::ENVIRONMENT_DOMAINS[$environment])) {
            return null;
        }

        return self::ENVIRONMENT_DOMAINS[$environment]['backend'];
    }
}
