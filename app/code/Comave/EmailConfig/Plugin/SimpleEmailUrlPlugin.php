<?php
declare(strict_types=1);

namespace Comave\EmailConfig\Plugin;

use Magento\Email\Model\Template;

/**
 * Simple plugin to rewrite URLs in email content from backend to frontend domain
 */
class SimpleEmailUrlPlugin
{
    private const DOMAIN_MAPPING = [
        'mcstaging.comave.com' => 'staging.comave.com',
        'mc.comave.com' => 'comave.com',
        'mcdev.comave.com' => 'dev.comave.com',
        'localhost:8080' => 'localhost:3000',
    ];

    /**
     * Rewrite URLs in final email content
     *
     * @param Template $subject
     * @param string $result
     * @return string
     */
    public function afterProcessTemplate(Template $subject, string $result): string
    {
        return $this->rewriteUrls($result);
    }

    /**
     * Rewrite URLs in email content
     *
     * @param string $content
     * @return string
     */
    private function rewriteUrls(string $content): string
    {
        foreach (self::DOMAIN_MAPPING as $backendDomain => $frontendDomain) {
            // Replace URLs in href attributes
            $content = preg_replace_callback(
                '/href=["\']([^"\']*' . preg_quote($backendDomain, '/') . '[^"\']*)["\']/',
                function ($matches) use ($backendDomain, $frontendDomain) {
                    $url = str_replace($backendDomain, $frontendDomain, $matches[1]);
                    return 'href="' . $url . '"';
                },
                $content
            );

            // Replace URLs in src attributes
            $content = preg_replace_callback(
                '/src=["\']([^"\']*' . preg_quote($backendDomain, '/') . '[^"\']*)["\']/',
                function ($matches) use ($backendDomain, $frontendDomain) {
                    $url = str_replace($backendDomain, $frontendDomain, $matches[1]);
                    return 'src="' . $url . '"';
                },
                $content
            );

            // Replace plain text URLs
            $content = str_replace(
                'https://' . $backendDomain,
                'https://' . $frontendDomain,
                $content
            );

            $content = str_replace(
                'http://' . $backendDomain,
                'http://' . $frontendDomain,
                $content
            );
        }

        return $content;
    }
}
